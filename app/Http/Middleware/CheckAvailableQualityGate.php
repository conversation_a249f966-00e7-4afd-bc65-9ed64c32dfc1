<?php

namespace App\Http\Middleware;

use App\Enums\ECommon;
use App\Repositories\Repository;
use App\Services\Service;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CheckAvailableQualityGate
{
    /**
     * Check available quality gate.
     *
     * @param  Request  $request
     * @param  Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $hasQualityGate = Repository::getQualityGate()->where([
            'id' => $request->route('quality_gate'),
            'project_id' => $request->route('projectId'),
        ])->exists();
        
        if (! $hasQualityGate) {
            return Service::response()->error(
                ECommon::RESPONSE_CODE_FAILURE,
                __('message.error.quality_gate.not_found'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        }

        return $next($request);
    }
}
