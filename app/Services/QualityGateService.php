<?php

namespace App\Services;

use App\Enums\ECommon;
use App\Repositories\Repository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Exception;

class QualityGateService
{
    /**
     * Get quality gates by project id with pagination
     *
     * @param  array  $params
     * @return mixed
     */
    public function list(array $params)
    {
        $conditions = $this->getConditions($params);

        return Repository::getQualityGate()->listPagination(
            $conditions,
            ['*'],
            ECommon::BUILDER_TYPE_ELOQUENT,
            ['project', 'sprint']
        );
    }

    /**
     * Get quality gate by id
     *
     * @param  int  $id
     * @return mixed
     */
    public function show(int $id)
    {
        $qualityGate = Repository::getQualityGate()->with(['project', 'sprint'])->find($id);
        
        if (!$qualityGate) {
            throw new ModelNotFoundException(__('message.error.quality_gate.not_found'));
        }

        return $qualityGate;
    }

    /**
     * Create a new quality gate
     *
     * @param  array  $data
     * @return mixed
     * @throws Exception
     */
    public function store($data)
    {
        // Check if quality gate already exists for this sprint
        $existingQualityGate = Repository::getQualityGate()->where([
            'sprint_id' => $data['sprint_id'],
            'project_id' => $data['project_id']
        ])->first();

        if ($existingQualityGate) {
            throw new Exception(__('message.error.quality_gate.already_exists_for_sprint'));
        }

        return Repository::getQualityGate()->create($data);
    }

    /**
     * Update quality gate
     *
     * @param  int  $id
     * @param  array  $data
     * @return mixed
     * @throws Exception
     */
    public function update(int $id, array $data)
    {
        $qualityGate = $this->show($id);

        // If sprint_id is being changed, check if another quality gate exists for the new sprint
        if (isset($data['sprint_id']) && $data['sprint_id'] != $qualityGate->sprint_id) {
            $existingQualityGate = Repository::getQualityGate()->where([
                'sprint_id' => $data['sprint_id'],
                'project_id' => $qualityGate->project_id
            ])->where('id', '!=', $id)->first();

            if ($existingQualityGate) {
                throw new Exception(__('message.error.quality_gate.already_exists_for_sprint'));
            }
        }

        return Repository::getQualityGate()->update($id, $data);
    }

    /**
     * Delete quality gate
     *
     * @param  int  $id
     * @return bool
     */
    public function delete(int $id)
    {
        $qualityGate = $this->show($id);
        return Repository::getQualityGate()->delete($id);
    }

    /**
     * Get conditions for filtering
     *
     * @param  array  $params
     * @return array
     */
    private function getConditions(array $params): array
    {
        $conditions = [];

        if (isset($params['project_id'])) {
            $conditions['project_id'] = $params['project_id'];
        }

        if (isset($params['sprint_id'])) {
            $conditions['sprint_id'] = $params['sprint_id'];
        }

        if (isset($params['quality_gate'])) {
            $conditions['quality_gate'] = $params['quality_gate'];
        }

        // Add pagination parameters
        $conditions['page'] = $params['page'] ?? 1;
        $conditions['per_page'] = $params['per_page'] ?? 15;

        return $conditions;
    }
}
