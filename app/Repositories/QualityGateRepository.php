<?php

namespace App\Repositories;

use App\Enums\ECommon;
use App\Interfaces\QualityGateRepositoryInterface;
use App\Models\QualityGate;

class QualityGateRepository extends BaseRepository implements QualityGateRepositoryInterface
{
    protected $columns = [
        'id',
        'project_id',
        'sprint_id',
        'quality_gate',
        'number_of_non_compliance',
        'number_of_process',
        'number_of_incident',
        'number_of_customer_complaint',
        'note',
        'created_at',
    ];

    protected $relations = ['project', 'sprint'];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * {@inheritDoc}
     */
    public function model()
    {
        return QualityGate::class;
    }

    public function getByProject($projectIds)
    {
        $condition = [
            'project_id' => $projectIds,
        ];

        return $this->list($condition, $this->columns, ECommon::BUILDER_TYPE_ELOQUENT, $this->relations);
    }
}
