<?php

namespace App\Http\Requests\QualityGate;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Enums\EQualityGate;

class UpdateQualityGateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'sprint_id' => [
                'nullable',
                'exists:sprints,id',
                Rule::unique('quality_gates')->where(function ($query) {
                    $query->where('project_id', $this->projectId);
                })->ignore($this->quality_gate),
            ],
            'quality_gate' => [
                'nullable',
                'integer',
                Rule::in(EQualityGate::QUALITY_GATE_OPTIONS),
            ],
            'number_of_non_compliance' => 'nullable|integer|min:0',
            'number_of_process' => 'nullable|integer|min:0',
            'number_of_incident' => 'nullable|integer|min:0',
            'number_of_customer_complaint' => 'nullable|integer|min:0',
            'note' => 'nullable|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'sprint_id.unique' => 'A quality gate already exists for this sprint.',
        ];
    }
}
