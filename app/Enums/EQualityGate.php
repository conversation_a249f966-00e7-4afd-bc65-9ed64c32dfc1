<?php

namespace App\Enums;

class EQualityGate
{
    const NOT_A<PERSON><PERSON><PERSON>LE_QUALITY_GATE = 0;
    const FAIL_QUALITY_GATE = 1;
    const PASS_QUALITY_GATE = 2;

    const QUALITY_GATE_OPTIONS = [
        self::NOT_AVAILABLE_QUALITY_GATE,
        self::FAIL_QUALITY_GATE,
        self::PASS_QUALITY_GATE,
    ];

    public static function getMasterData()
    {
        return [
            'na' => self::NOT_AVAILABLE_QUALITY_GATE,
            'fail' => self::FAIL_QUALITY_GATE,
            'pass' => self::PASS_QUALITY_GATE,
        ];
    }
}