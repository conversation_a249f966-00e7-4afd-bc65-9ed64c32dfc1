<?php

namespace App\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class QualityGateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'project_id' => $this->project_id,
            'sprint_id' => $this->sprint_id,
            'quality_gate' => $this->quality_gate,
            'number_of_non_compliance' => $this->number_of_non_compliance,
            'number_of_process' => $this->number_of_process,
            'number_of_incident' => $this->number_of_incident,
            'number_of_customer_complaint' => $this->number_of_customer_complaint,
            'note' => $this->note,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'project' => $this->whenLoaded('project'),
            'sprint' => $this->whenLoaded('sprint'),
        ];
    }
}
