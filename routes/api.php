<?php

use App\Http\Controllers\ActivityLogController;
use App\Http\Controllers\AdminPermissionController;
use App\Http\Controllers\AllocationController;
use App\Http\Controllers\ChargeRateController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\DailyReportController;
use App\Http\Controllers\DeliverableController;
use App\Http\Controllers\DivisionController;
use App\Http\Controllers\FunctionController;
use App\Http\Controllers\LevelController;
use App\Http\Controllers\QualityGateController;
use App\Http\Controllers\MasterDataController;
use App\Http\Controllers\MilestoneController;
use App\Http\Controllers\PcvReportController;
use App\Http\Controllers\PmReportController;
use App\Http\Controllers\PositionController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\RequirementChangeController;
use App\Http\Controllers\ResourceRentalCostController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\StageController;
use App\Http\Controllers\TechnologyController;
use App\Http\Controllers\UserUnpaidLeaveController;
use App\Http\Controllers\DivisionModuleController;
use App\Http\Controllers\RuleController;
use App\Http\Controllers\PenaltyController;
use App\Http\Controllers\RulePenaltyController;
use App\Http\Controllers\ViolationReportController;
use App\Http\Controllers\WeeklyReportController;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('projects')
    ->group(function () {
        Route::resource('', ProjectController::class)->parameters(['' => 'projects']);
        Route::get('/{id}/members', [ProjectController::class, 'getMember']);
        Route::get('/{id}/members-by-roles', [ProjectController::class, 'getMemberIdsByRoles']);
        Route::post('/{id}/members', [ProjectController::class, 'addMember']);
        Route::get('/{id}/overview', [ProjectController::class, 'overview']);
        Route::get('/{id}/expense-ee', [ProjectController::class, 'expenseEE']);
        Route::delete('/{project_id}/members/{member_id}', [ProjectController::class, 'removeMember']);
        Route::resource('{id}/pcv-reports', PcvReportController::class);
        Route::get('/{id}/weeks', [ProjectController::class, 'getWeeks']);
        Route::get('{id}/schedule', [StageController::class, 'getSchedule']);
        Route::resource('{project_id}/requirement-change', RequirementChangeController::class);
        Route::post('{id}/requirement-change/import', [RequirementChangeController::class, 'import']);
        Route::post('/{project_id}/requirement-change/{requirement_change_id}', [RequirementChangeController::class, 'update']);
        Route::delete('/{project_id}/requirement-change', [RequirementChangeController::class, 'destroy']);
        Route::put('/{id}/budget', [ProjectController::class, 'updateBudgetForYear']);
        Route::get('/{id}/budget', [ProjectController::class, 'getBudgetForYear']);
        Route::get('/{id}/budget-all', [ProjectController::class, 'getAllBudget']);
        Route::get('/{id}/data-ee-chart', [ProjectController::class, 'getDataEEChart']);

        Route::prefix('{project_id}/stages')
            ->group(function () {
                Route::resource('', StageController::class)->parameters(['' => 'stages']);
                Route::resource('{id}/allocations', AllocationController::class);
                Route::resource('{id}/milestones', MilestoneController::class);
                Route::resource('{id}/deliverables', DeliverableController::class);
                Route::post('{id}/clone', [StageController::class, 'clone']);
            });
        Route::resource('{project_id}/pm-report', PmReportController::class);
        Route::get('{project_id}/pm-reported', [PmReportController::class, 'getDateReported']);
        Route::get('{id}/deliverables', [ProjectController::class, 'getListDeliverable']);
        Route::get('{id}/milestones', [ProjectController::class, 'getListMilestone']);
        Route::prefix('{projectId}')->middleware('check_available_project')->group(function () {
            Route::resource('functions', FunctionController::class);
            Route::prefix('performance')->group(function () {
                Route::get('', [ProjectController::class, 'getPerformanceStatistic']);
                Route::post('import', [ProjectController::class, 'importPerformanceData']);
            });
        });
    });

Route::resource('resource-rental-costs', ResourceRentalCostController::class);
Route::get('/all-project', [ProjectController::class, 'getAllProject']);
Route::match(['get', 'post'], '/list-project', [ProjectController::class, 'getProjects']);
Route::get('/user-projects', [ProjectController::class, 'getProjectsOfUser']);
Route::resource('/technologies', TechnologyController::class);
Route::get('/master-data', [MasterDataController::class, 'getMasterData']);
Route::resource('customers', CustomerController::class);
Route::get('list-customer', [CustomerController::class, 'getCustomers']);
Route::resource('roles', RoleController::class);
Route::post('user-allocations', [AllocationController::class, 'getAllocationByUserIds']);
Route::post('project-allocations', [AllocationController::class, 'getAllocationByProjectIds']);
Route::get('month-allocations', [AllocationController::class, 'getAllocationInMonth']);
Route::get('year-allocations', [AllocationController::class, 'getAllocationInYear']);
Route::get('log-activities/{id}', [ActivityLogController::class, 'index']);
Route::get('log-activities', [ActivityLogController::class, 'index']);
Route::resource('positions', PositionController::class);
Route::resource('divisions', DivisionController::class);
Route::resource('levels', LevelController::class);
Route::get('user-belong-to-project', [ProjectController::class, 'userBelongToProject']);
Route::get('pm-reports', [PmReportController::class, 'getAllPmReports']);
Route::resource('user-unpaid-leave', UserUnpaidLeaveController::class);
Route::get('user-unpaid-leave-by-time', [UserUnpaidLeaveController::class, 'userUnpaidLeaveByTime']);
Route::get('/allocation/list', [AllocationController::class, 'getListAllocation']);

Route::resource('daily-reports', DailyReportController::class);

Route::put('/change-status-daily-reports', [DailyReportController::class, 'changeStatus']);
Route::get('/resources/allocations/{user_id}', [ProjectController::class, 'getProjectAllocations']);
Route::get('/resources/stats/{user_id}', [ProjectController::class, 'getProjectStats']);
Route::post('update-project-wf', [ProjectController::class, 'updateProjectWF']);
Route::post('/project-members', [ProjectController::class, 'getProjectMembers']);

Route::post('permission/positions', [AdminPermissionController::class, 'positions'])->name('positions');
Route::post('permission/roles', [AdminPermissionController::class, 'roles'])->name('roles');

Route::post('/customers-sync', function () {
    Artisan::call('customers:sync');
    return true;
});

Route::post('/permission-sync', function () {
    Artisan::call('permission:sync');
    return true;
});

Route::get('/health-check', function () {
    return \Illuminate\Http\Response::HTTP_OK;
});

Route::resource('daily-reports', DailyReportController::class);
Route::get('/user-daily-reports', [DailyReportController::class, 'getReports']);
// use Route::post for large request
Route::post('/list-daily-reports', [DailyReportController::class, 'getReports']);

Route::put('/change-status-daily-reports', [DailyReportController::class, 'changeStatus']);

Route::post('project-daily-reports', [DailyReportController::class, 'getDailyReportByProjectIds']);
Route::post('user-daily-reports', [DailyReportController::class, 'getDailyReportByUserIds']);
Route::get('project-statistics', [ProjectController::class, 'getProjectStatistics']);
Route::get('project-budgets', [ProjectController::class, 'getProjectBudgets']);
Route::get('charge-rate-statistics', [ChargeRateController::class, 'getChargeRateStatistics']);
Route::get('check-division-project/{division_id}/{project_id}', [ProjectController::class, 'checkDivisionProject']);

// search keyword in project name or customer name
Route::get('list-projects-by-keyword', [ProjectController::class, 'getProjectsByKeyword']);

// search projects name by customer_id
Route::get('list-projects-by-customer', [ProjectController::class, 'getProjectsByCustomer']);

Route::get('/raw-projects', [ProjectController::class, 'getRawProjects']);

// get customers of division projects
Route::get('division-customers', [CustomerController::class, 'getDivisionCustomers']);
Route::prefix('charge-rate')
    ->group(
        function () {
            Route::get('month-budget', [ChargeRateController::class, 'getMonthBudget']);
            Route::get('division-month-budgets', [ChargeRateController::class, 'getDivisionMonthBudgets']);
        }
    );

// use Post for large request params
Route::post('raw-allocations', [AllocationController::class, 'getRawAllocations']);
Route::post('list-daily-reports-and-allocations', [DivisionModuleController::class, 'getDailyReportsAndAllocations']);
Route::get('/worklog-report', [ProjectController::class, 'getWorklogReport']);
Route::get('/project-month-budget', [ProjectController::class, 'getProjectMonthBudget']);
Route::get('/project-by-code', [ProjectController::class, 'getProjectByCode']);

Route::resource('tasks', TaskController::class);
Route::post('list-task', [TaskController::class, 'index']);

Route::resource('rules', RuleController::class);
Route::resource('penalties', PenaltyController::class);
Route::resource('rule-penalties', RulePenaltyController::class);
Route::resource('violation-reports', ViolationReportController::class);
Route::resource('weekly-reports', WeeklyReportController::class);
