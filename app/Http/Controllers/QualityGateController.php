<?php

namespace App\Http\Controllers;

use App\Http\Requests\QualityGate\QualityGateListRequest;
use App\Http\Requests\QualityGate\StoreQualityGateRequest;
use App\Http\Requests\QualityGate\UpdateQualityGateRequest;
use App\Resources\QualityGateResource;
use App\Services\QualityGateService;
use Illuminate\Http\JsonResponse;

class QualityGateController extends Controller
{
    private $qualityGateService;

    public function __construct(QualityGateService $qualityGateService)
    {
        $this->middleware('check_available_quality_gate')->except(['index', 'store']);
        $this->qualityGateService = $qualityGateService;
    }

    /**
     * Display a listing of the quality gates.
     *
     * @param  int  $projectId
     * @param  QualityGateListRequest  $request
     * @return JsonResponse
     */
    public function index(int $projectId, QualityGateListRequest $request)
    {
        $request->merge(['project_id' => $projectId]);
        $qualityGates = $this->qualityGateService->list($request->all());

        return $this->response()->success(
            __('message.success.quality_gate.list'),
            QualityGateResource::collection($qualityGates)
        );
    }

    /**
     * Store a newly created quality gate in storage.
     *
     * @param  int  $projectId
     * @param  StoreQualityGateRequest  $request
     * @return JsonResponse
     */
    public function store(int $projectId, StoreQualityGateRequest $request)
    {
        try {
            $request->merge(['project_id' => $projectId]);

            $qualityGate = $this->qualityGateService->store($request->all());
            $qualityGate->load(['project', 'sprint']);

            return $this->response()->success(
                __('message.success.quality_gate.create'),
                new QualityGateResource($qualityGate)
            );
        } catch (\Exception $e) {
            return $this->response()->error(
                'FAILURE',
                $e->getMessage(),
                422
            );
        }
    }

    /**
     * Display the specified quality gate.
     *
     * @param  int  $projectId
     * @param  int  $id
     * @return JsonResponse
     */
    public function show(int $projectId, int $id)
    {
        try {
            $qualityGate = $this->qualityGateService->show($id);

            return $this->response()->success(
                __('message.success.quality_gate.detail'),
                new QualityGateResource($qualityGate)
            );
        } catch (\Exception $e) {
            return $this->response()->error(
                'FAILURE',
                $e->getMessage(),
                404
            );
        }
    }

    /**
     * Update the specified quality gate in storage.
     *
     * @param  int  $projectId
     * @param  int  $id
     * @param  UpdateQualityGateRequest  $request
     * @return JsonResponse
     */
    public function update(int $projectId, int $id, UpdateQualityGateRequest $request)
    {
        try {
            $qualityGate = $this->qualityGateService->update($id, $request->except('project_id'));
            $qualityGate->load(['project', 'sprint']);

            return $this->response()->success(
                __('message.success.quality_gate.update'),
                new QualityGateResource($qualityGate)
            );
        } catch (\Exception $e) {
            return $this->response()->error(
                'FAILURE',
                $e->getMessage(),
                422
            );
        }
    }

    /**
     * Remove the specified quality gate from storage.
     *
     * @param  int  $projectId
     * @param  int  $id
     * @return JsonResponse
     */
    public function destroy(int $projectId, int $id)
    {
        try {
            $this->qualityGateService->delete($id);

            return $this->response()->success(
                __('message.success.quality_gate.delete')
            );
        } catch (\Exception $e) {
            return $this->response()->error(
                'FAILURE',
                $e->getMessage(),
                404
            );
        }
    }
}
